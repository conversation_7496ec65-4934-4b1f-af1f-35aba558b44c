{"version": 3, "sources": ["components/main-form/components/form-datetime.uvue", "components/main-form/components/form-input.uvue"], "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<view class=\"datetime-display-container\" @click=\"openDateTimePicker\">\n\t\t\t\t<text class=\"datetime-text\" v-if=\"displayValue !== ''\">{{ displayValue }}</text>\n\t\t\t\t<text class=\"datetime-placeholder\" v-if=\"displayValue === ''\">请选择时间</text>\n\t\t\t</view>\n\t\t</template>\n\t</form-container>\n\n\t<!-- 时间选择器 -->\n\t<MainDateTimePicker ref=\"datetimePicker\" :mode=\"varType\" @confirm=\"onDateTimeConfirm\" @cancel=\"onDateTimeCancel\"></MainDateTimePicker>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\timport MainDateTimePicker from './../tools/main-datetime-picker.uvue'\n\n\t// 定义模式类型\n\ttype DateTimeMode = 'datetime' | 'date' | 'time' | 'year' | 'year-month' | 'month' | 'day' | 'hour-minute' | 'hour-minute-second' | 'datetime-range' | 'date-range' | 'time-range'\n\n\texport default {\n\t\tname: \"FormDateTime\",\n\t\temits: ['change'],\n\t\tcomponents: {\n\t\t\tFormContainer,\n\t\t\tMainDateTimePicker\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: null as any as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: \"\" as string,\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tvarType: \"datetime\" as DateTimeMode,\n\t\t\t\tdisplayValue: \"\",\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\t// 这避免了用户输入时的循环更新问题\n\t\t\t\t\tconst newValue = obj.value as string\n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t\tthis.updateDisplayValue()\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value as string\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\t\t\t\tthis.varType = extalJson.getString(\"varType\") ?? \"datetime\" as DateTimeMode\n\n\t\t\t\t// 更新显示值\n\t\t\t\tthis.updateDisplayValue()\n\n\t\t\t\t// 获取缓存\n\t\t\t\tthis.getCache()\n\t\t\t},\n\n\t\t\t// 更新显示值\n\t\t\tupdateDisplayValue(): void {\n\t\t\t\tif (this.fieldValue != \"\") {\n\t\t\t\t\tthis.displayValue = this.fieldValue as string\n\t\t\t\t} else {\n\t\t\t\t\tthis.displayValue = \"\"\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst save_value = res.data\n\t\t\t\t\t\t\tif(typeof save_value === 'string'){\n\t\t\t\t\t\t\t\tthat.fieldValue = save_value as string\n\t\t\t\t\t\t\t\tthat.updateDisplayValue()\n\t\t\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\t\t\tvalue: save_value\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void {\n\t\t\t\tif (this.isSave && typeof this.fieldValue ===\"string\") {\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: this.fieldValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 时间值验证\n\t\t\t\tif (this.fieldValue == \"\") {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = \"请选择时间\"\n\t\t\t\t\treturn false\n\t\t\t\t}\n\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value as string\n\t\t\t\t// 更新显示值\n\t\t\t\tthis.updateDisplayValue()\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t},\n\n\t\t\t// 打开时间选择器\n\t\t\topenDateTimePicker(): void {\n\t\t\t\tconst datetimePicker = this.$refs[\"datetimePicker\"] as ComponentPublicInstance\n\t\t\t\t// 如果有值，传递给选择器\n\t\t\t\tif (this.fieldValue != \"\") {\n\t\t\t\t\tdatetimePicker.$callMethod(\"show\", this.fieldValue)\n\t\t\t\t} else {\n\t\t\t\t\tdatetimePicker.$callMethod(\"show\")\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 时间选择确认\n\t\t\tonDateTimeConfirm(dateTimeData: UTSJSONObject): void {\n\t\t\t\t// 使用formatted的值作为最终结果\n\t\t\t\tconst selectedDateTime = dateTimeData.getString(\"formatted\") ?? \"\"\n\n\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\tindex: this.index,\n\t\t\t\t\tvalue: selectedDateTime\n\t\t\t\t}\n\t\t\t\tthis.change(result)\n\t\t\t},\n\n\t\t\t// 时间选择取消\n\t\t\tonDateTimeCancel(): void {\n\t\t\t\t// 取消选择，不做任何操作\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.datetime-display-container {\n\t\tflex: 1;\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tpadding: 0 20rpx;\n\t\theight: 60rpx;\n\t\tborder-radius: 10rpx;\n\t\tbackground-color: rgba(255, 255, 255, 0.8);\n\t}\n\n\t.datetime-text {\n\t\tflex: 1;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\ttext-align: left;\n\t}\n\n\t.datetime-placeholder {\n\t\tflex: 1;\n\t\tfont-size: 28rpx;\n\t\tcolor: #999999;\n\t\ttext-align: center;\n\t}\n</style>", null], "names": [], "mappings": ";;;;;;;;;;;;;+BA8GU;;AAvFJ;;kBA0DJ,OAAW,IAAG,CAAA;YAEb,IAAM,WAAW,IAAI,CAAC,QAAM,CAAC,OAAM,CAAA,EAAA;YACnC,IAAI,CAAC,aAAa,CAAC;QACpB;;;;;UAhBE,IAAQ,kBAAkB,EAAA;YAGzB,IAAM,WAAW,IAAI,KAAI,CAAA,EAAA,CAAK,MAAK;YACnC,IAAI,aAAa,IAAI,CAAC,UAAU,EAAE;gBACjC,IAAI,CAAC,UAAS,GAAI;gBAClB,IAAI,CAAC,kBAAkB;;QAEzB;uBACA,OAAM,IAAG;;;;;;;;;YA7EZ,IAQiB,2BAAA,IARA,WAAO,KAAA,SAAS,EAAG,gBAAY,KAAA,SAAS,EAAG,SAAK,KAAA,GAAG,EAAG,mBAAe,KAAA,YAAY,EAAG,iBAAa,KAAA,UAAU,EAC1H,sBAAkB,KAAA,eAAe,OACvB,mBAAa,YACvB,gBAGO,GAAA;uBAAA;oBAHP,IAGO,QAAA,IAHD,WAAM,8BAA8B,aAAO,KAAA,kBAAkB;wBAChC,IAAA,KAAA,YAAY,KAAA;4BAA9C,IAAgF,QAAA,gBAA1E,WAAM,sBAA8C,KAAA,YAAY,GAAA,CAAA;;;;;wBAC7B,IAAA,KAAA,YAAY,KAAA;4BAArD,IAA0E,QAAA,gBAApE,WAAM,yBAAkD;;;;;;;;;;;;;;;;;YAMjE,IAAsI,+BAAA,IAAlH,SAAI,kBAAkB,UAAM,KAAA,OAAO,EAAG,eAAS,KAAA,iBAAiB,EAAG,cAAQ,KAAA,gBAAgB;;;;;;;;;;;;aAyC5G;aACA,YAAkB,MAAM;aACxB;aACA;aACA;aACA,SAAuB;aACvB;aACA;aACA;;;mBARA,eAAW,IACX,gBAAY,GAAC,EAAA,CAAK,MAAM,EACxB,YAAQ,KAAK,EACb,cAAU,IACV,SAAK,IACL,aAAS,WAAS,EAAA,CAAK,eACvB,kBAAc,IACd,eAAW,KAAK,EAChB,kBAAc;;aA2Bf;aAAA,qBAAc,uBAAuB,GAAG,IAAG,CAAA;QAC1C,IAAM,WAAW,SAAS,GAAE;QAC5B,IAAM,aAAa,SAAS,KAAI,CAAA,EAAA,CAAK,MAAK;QAG1C,IAAI,CAAC,SAAQ,GAAI,SAAS,IAAG;QAC7B,IAAI,CAAC,UAAS,GAAI;QAClB,IAAI,CAAC,MAAK,GAAI,SAAS,MAAK,IAAK,KAAI;QACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,MAAM;QAGrC,IAAM,YAAY,SAAS,KAAI,CAAA,EAAA,CAAK;QACpC,IAAI,CAAC,GAAE,GAAI,UAAU,SAAS,CAAC,UAAU;QACzC,IAAI,CAAC,OAAM,GAAI,UAAU,SAAS,CAAC,cAAc,WAAS,EAAA,CAAK;QAG/D,IAAI,CAAC,kBAAkB;QAGvB,IAAI,CAAC,QAAQ;IACd;aAGA;aAAA,6BAAsB,IAAG,CAAA;QACxB,IAAI,IAAI,CAAC,UAAS,IAAK,IAAI;YAC1B,IAAI,CAAC,YAAW,GAAI,IAAI,CAAC,UAAS,CAAA,EAAA,CAAK,MAAK;eACtC;YACN,IAAI,CAAC,YAAW,GAAI;;IAEtB;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YAChB,IAAM,OAAO,IAAG;YAChB,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,UAAS,IAAC,KAAK,kBAAoB;gBAClC,IAAM,aAAa,IAAI,IAAG;gBAC1B,IAAG,oBAAO,gBAAe,UAAS;oBACjC,KAAK,UAAS,GAAI,WAAS,EAAA,CAAK,MAAK;oBACrC,KAAK,kBAAkB;oBACvB,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;oBAER,IAAI,CAAC,MAAM,CAAC;;YAGd;;;IAGH;aAEA;aAAA,mBAAY,IAAG,CAAA;QACd,IAAI,IAAI,CAAC,MAAK,IAAK,oBAAO,IAAI,CAAC,UAAS,MAAK,UAAU;YACtD,iCACC,MAAK,IAAI,CAAC,QAAQ,EAClB,OAAM,IAAI,CAAC,UAAS;;IAGvB;aAEA;aAAA,mBAAY,OAAM,CAAA;QAEjB,IAAI,IAAI,CAAC,UAAS,IAAK,IAAI;YAC1B,IAAI,CAAC,SAAQ,GAAI,IAAG;YACpB,IAAI,CAAC,YAAW,GAAI;YACpB,OAAO,KAAI;;QAGZ,IAAI,CAAC,SAAQ,GAAI,KAAI;QACrB,IAAI,CAAC,YAAW,GAAI;QACpB,OAAO,IAAG;IACX;aAEA;aAAA,cAAO,sBAAsB,GAAG,IAAG,CAAA;QAElC,IAAI,CAAC,UAAS,GAAI,MAAM,KAAI,CAAA,EAAA,CAAK,MAAK;QAEtC,IAAI,CAAC,kBAAkB;QAEvB,IAAI,CAAC,QAAQ;QAEb,IAAI,CAAC,OAAK,CAAC,UAAU;IACtB;aAGA;aAAA,6BAAsB,IAAG,CAAA;QACxB,IAAM,iBAAiB,IAAI,CAAC,OAAK,CAAC,iBAAgB,CAAA,EAAA,CAAK;QAEvD,IAAI,IAAI,CAAC,UAAS,IAAK,IAAI;YAC1B,eAAe,aAAW,CAAC,QAAQ,IAAI,CAAC,UAAU;eAC5C;YACN,eAAe,aAAW,CAAC;;IAE7B;aAGA;aAAA,yBAAkB,cAAc,aAAa,GAAG,IAAG,CAAA;QAElD,IAAM,mBAAmB,aAAa,SAAS,CAAC,gBAAgB;QAEhE,IAAM,yBACL,QAAO,IAAI,CAAC,KAAK,EACjB,QAAO;QAER,IAAI,CAAC,MAAM,CAAC;IACb;aAGA;aAAA,2BAAoB,IAAG,CAAA,CAEvB;;mBAhLK;;;;;;;;;;;;;6FAYK,CAAA,qDAIA,0DAIA,mEAIA;;;;;;;;;AA0JZ"}