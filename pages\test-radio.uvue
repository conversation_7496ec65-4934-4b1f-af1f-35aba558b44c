<template>
	<!-- #ifdef APP -->
	<scroll-view class="container">
	<!-- #endif -->
		<view class="page">
			<view class="header">
				<text class="title">Form Radio 组件测试</text>
			</view>
			
			<main-form :formData="formData" keyName="test" @change="onFormChange"></main-form>
			
			<view class="result">
				<text class="result-title">选择结果：</text>
				<text class="result-text">{{ resultText }}</text>
			</view>
		</view>
	<!-- #ifdef APP -->
	</scroll-view>
	<!-- #endif -->
</template>

<script lang="uts">
	import MainForm from '@/components/main-form/main-form.uvue'
	import { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'

	export default {
		components: {
			MainForm
		},
		data() {
			return {
				resultText: "未选择",
				formData: [
					{
						key: "gender",
						name: "性别选择",
						type: "radio",
						value: null,
						isSave: false,
						extra: {
							tip: "请选择您的性别",
							varType: "string",
							radioColor: "#007aff",
							options: [
								{ text: "男", value: "male" },
								{ text: "女", value: "female" },
								{ text: "其他", value: "other" }
							]
						}
					},
					{
						key: "level",
						name: "等级选择",
						type: "radio", 
						value: null,
						isSave: false,
						extra: {
							tip: "请选择您的等级",
							varType: "number",
							radioColor: "#ff6b35",
							options: [
								{ text: "初级", value: 1 },
								{ text: "中级", value: 2 },
								{ text: "高级", value: 3 },
								{ text: "专家", value: 4 }
							]
						}
					}
				] as FormFieldData[]
			}
		},
		methods: {
			onFormChange(event: FormChangeEvent): void {
				console.log('表单变化:', event)
				
				// 更新对应字段的值
				if (event.index < this.formData.length) {
					this.formData[event.index].value = event.value
				}
				
				// 更新结果显示
				this.updateResultText()
			},
			
			updateResultText(): void {
				const results: string[] = []
				
				this.formData.forEach((item: FormFieldData) => {
					if (item.value != null) {
						const optionsArray = item.extra.getArray("options")
						if (optionsArray != null) {
							for (let i = 0; i < optionsArray.length; i++) {
								const optionObj = optionsArray[i] as UTSJSONObject
								const optionValue = optionObj.get("value")
								if (optionValue == item.value) {
									const optionText = optionObj.getString("text") ?? ""
									results.push(`${item.name}: ${optionText}`)
									break
								}
							}
						}
					}
				})
				
				this.resultText = results.length > 0 ? results.join(", ") : "未选择"
			}
		}
	}
</script>

<style>
	.container {
		flex: 1;
	}
	
	.page {
		padding: 40rpx;
		background-color: #f5f5f5;
		min-height: 100vh;
	}
	
	.header {
		margin-bottom: 40rpx;
		text-align: center;
	}
	
	.title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}
	
	.result {
		margin-top: 40rpx;
		padding: 30rpx;
		background-color: #fff;
		border-radius: 10rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
	}
	
	.result-title {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
		margin-bottom: 10rpx;
	}
	
	.result-text {
		font-size: 26rpx;
		color: #666;
		line-height: 1.5;
	}
</style>
