{"version": 3, "sources": ["components/main-form/components/form-radio.uvue"], "names": [], "mappings": "AAmBC,OAAO,EAAE,aAAa,EAAE,eAAc,EAAE,MAAO,sCAAqC,CAAA;AACpF,OAAO,aAAY,MAAO,uBAAsB,CAAA;AAEhD,SAAQ;AACR,KAAK,WAAU,GAAI;IAAA,mBAAA,CAAA,EAAA,oBAAA,CAAA,aAAA,EAAA,iDAAA,EAAA,EAAA,EAAA,CAAA,CAAA,CAAA;IAClB,IAAI,EAAE,MAAM,CAAA;IACZ,KAAK,EAAE,MAAK,GAAI,MAAM,CAAA;CACvB,CAAA;AAEA,MAAK,OAAQ,GAAE,eAAA,CAAA;IACd,IAAI,EAAE,WAAW;IACjB,KAAK,EAAE,CAAC,QAAQ,EAAE,KAAK,CAAC;IACxB,UAAU,EAAE;QACX,aAAY;KACZ;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,IAAI,EAAE,IAAG,IAAK,GAAE,IAAK,QAAQ,CAAC,aAAa,CAAA;SAC3C;QACD,KAAK,EAAE;YACN,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,CAAA;SACT;QACD,OAAO,EAAE;YACR,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,EAAC;SACV;QACD,UAAU,EAAE;YACX,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,MAAK;SACd;QACD,eAAe,EAAE;YAChB,IAAI,EAAE,MAAM;YACZ,OAAO,EAAE,SAAQ;SAClB;KACA;IACD,IAAI;QACH,OAAO;YACN,SAAS,EAAE,EAAE;YACb,UAAU,EAAE,IAAG,IAAK,MAAK,GAAI,MAAK,GAAI,IAAI;YAC1C,MAAM,EAAE,KAAK;YACb,QAAQ,EAAE,EAAE;YACZ,GAAG,EAAE,EAAE;YACP,OAAO,EAAE,QAAQ;YACjB,YAAY,EAAE,EAAC,IAAK,WAAW,EAAE;YACjC,UAAU,EAAE,SAAS;YACrB,SAAS,EAAE,KAAK;YAChB,YAAY,EAAE,EAAC;SAChB,CAAA;IACD,CAAC;IACD,QAAQ,EAAE,EAET;IACD,KAAK,EAAE;QACN,IAAI,EAAE;YACL,OAAO,CAAC,GAAG,EAAE,aAAa;gBACzB,wDAAuD;gBACvD,MAAM,QAAO,GAAI,GAAG,CAAC,KAAI,CAAA;gBACzB,IAAI,QAAO,KAAM,IAAI,CAAC,UAAU,EAAE;oBACjC,IAAI,CAAC,UAAS,GAAI,QAAO,CAAA;iBAC1B;YACD,CAAC;YACD,IAAI,EAAE,IAAG;SACV;KACA;IACD,OAAO,IAAI,IAAG;QACb,aAAY;QACZ,MAAM,QAAO,GAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,IAAK,aAAY,CAAA;QACpD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAA,CAAA;IAC5B,CAAC;IACD,OAAO,EAAE;QACR,qBAAoB;QACpB,aAAa,CAAC,QAAQ,EAAE,aAAa,GAAG,IAAG;YAC1C,MAAM,QAAO,GAAI,QAAQ,CAAC,GAAE,CAAA;YAC5B,MAAM,UAAS,GAAI,QAAQ,CAAC,KAAI,CAAA;YAEhC,SAAQ;YACR,IAAI,CAAC,SAAQ,GAAI,QAAQ,CAAC,IAAG,CAAA;YAC7B,IAAI,CAAC,UAAS,GAAI,UAAS,CAAA;YAC3B,IAAI,CAAC,MAAK,GAAI,QAAQ,CAAC,MAAK,IAAK,KAAI,CAAA;YACrC,IAAI,CAAC,QAAO,GAAI,IAAI,CAAC,OAAM,GAAI,GAAE,GAAI,QAAO,CAAA;YAE5C,SAAQ;YACR,MAAM,SAAQ,GAAI,QAAQ,CAAC,KAAI,IAAK,aAAY,CAAA;YAChD,IAAI,CAAC,GAAE,GAAI,SAAS,CAAC,SAAS,CAAC,KAAK,CAAA,IAAK,EAAC,CAAA;YAC1C,MAAM,aAAY,GAAI,SAAS,CAAC,SAAS,CAAC,SAAS,CAAA,IAAK,QAAO,CAAA;YAE/D,kBAAiB;YACjB,IAAI,CAAC,OAAM,GAAI,IAAI,CAAC,eAAe,CAAC,aAAa,CAAA,CAAA;YACjD,IAAI,CAAC,UAAS,GAAI,SAAS,CAAC,SAAS,CAAC,YAAY,CAAA,IAAK,SAAQ,CAAA;YAE/D,SAAQ;YACR,MAAM,YAAW,GAAI,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAA,CAAA;YACjD,IAAI,YAAW,IAAK,IAAI,EAAE;gBACzB,IAAI,CAAC,YAAW,GAAI,EAAC,CAAA;gBACrB,KAAK,IAAI,CAAA,GAAI,CAAC,EAAE,CAAA,GAAI,YAAY,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC7C,MAAM,SAAQ,GAAI,YAAY,CAAC,CAAC,CAAA,IAAK,aAAY,CAAA;oBACjD,MAAM,MAAM,EAAE,WAAU,GAAI;wBAC3B,IAAI,EAAE,SAAS,CAAC,SAAS,CAAC,MAAM,CAAA,IAAK,EAAE;wBACvC,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC,OAAO,CAAA,IAAK,EAAC;qBACnC,CAAA;oBACA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAA,CAAA;iBAC9B;aACD;YAEA,OAAM;YACN,IAAI,CAAC,QAAQ,EAAC,CAAA;QACf,CAAC;QAED,kBAAiB;QACjB,eAAe,CAAC,OAAO,EAAE,MAAM,GAAG,MAAK;YACtC,MAAM,UAAS,GAAI,CAAC,QAAQ,EAAE,QAAQ,CAAA,CAAA;YACtC,IAAI,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;gBACjC,OAAO,OAAM,CAAA;aACd;iBAAO;gBACN,OAAO,QAAO,CAAA;aACf;QACD,CAAC;QAED,YAAW;QACX,SAAS,CAAC,WAAW,EAAE,MAAK,GAAI,MAAM,GAAG,OAAM;YAC9C,IAAI,IAAI,CAAC,UAAS,IAAK,IAAI,EAAE;gBAC5B,OAAO,KAAI,CAAA;aACZ;YACA,gBAAe;YACf,OAAO,WAAU,IAAK,IAAI,CAAC,UAAS,CAAA;QACrC,CAAC;QAED,OAAM;QACN,YAAY,CAAC,WAAW,EAAE,MAAK,GAAI,MAAM,GAAG,IAAG;YAC9C,IAAI,aAAa,EAAE,MAAK,GAAI,MAAK,GAAI,WAAU,CAAA;YAE/C,gBAAe;YACf,IAAI,IAAI,CAAC,OAAM,IAAK,QAAQ,EAAE;gBAC7B,IAAI,OAAO,aAAY,KAAM,QAAQ,EAAE;oBACtC,aAAY,GAAI,aAAY,UAAA,CAAA;iBAC7B;qBAAO;oBACN,aAAY,GAAI,UAAU,CAAC,CAAA,aAAa,YAAC,QAAQ,EAAE,CAAA,CAAA;oBACnD,IAAI,KAAK,CAAC,aAAY,IAAK,MAAM,CAAC,EAAE;wBACnC,aAAY,GAAI,CAAA,CAAA;qBACjB;iBACD;aACD;iBAAO;gBACN,sBAAqB;gBACrB,aAAY,GAAI,aAAa,CAAC,QAAQ,EAAC,CAAA;aACxC;YAEA,MAAM,MAAM,EAAE,eAAc,GAAI;gBAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;gBACjB,KAAK,EAAE,aAAY;aACpB,CAAA;YACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,CAAA;QACnB,CAAC;QAED,yBAAwB;QACxB,aAAa,CAAC,KAAK,EAAE,GAAG,GAAG,IAAG;YAC7B,MAAM,aAAY,GAAI,KAAK,CAAC,MAAM,CAAC,KAAI,CAAA;YACvC,UAAS;YACT,MAAM,cAAa,GAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,WAAW,GAAG,OAAM,CAAE,EAAC;gBAC7E,OAAO,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAC,IAAK,aAAY,CAAA;YAC/C,CAAC,CAAA,CAAA;YAED,IAAI,cAAa,IAAK,IAAI,EAAE;gBAC3B,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,KAAK,CAAA,CAAA;aACvC;QACD,CAAC;QAED,QAAQ,IAAI,IAAG;YACd,IAAI,IAAI,CAAC,MAAM,EAAE;gBAChB,MAAM,IAAG,GAAI,IAAG,CAAA;gBAChB,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,OAAO,EAAE,CAAC,GAAG,EAAE,iBAAiB,EAAE,EAAC;wBAClC,MAAM,SAAQ,GAAI,GAAG,CAAC,IAAG,IAAK,MAAK,CAAA;wBACnC,IAAI,UAAU,EAAE,MAAK,GAAI,MAAK,CAAA;wBAE9B,gBAAe;wBACf,IAAI,IAAI,CAAC,OAAM,IAAK,QAAQ,EAAE;4BAC7B,UAAS,GAAI,UAAU,CAAC,SAAS,CAAA,CAAA;4BACjC,SAAQ;4BACR,IAAI,KAAK,CAAC,UAAS,IAAK,MAAM,CAAC,EAAE;gCAChC,OAAK,CAAE,aAAY;6BACpB;yBACD;6BAAO;4BACN,sBAAqB;4BACrB,UAAS,GAAI,SAAQ,CAAA;yBACtB;wBAEA,IAAI,CAAC,UAAS,GAAI,UAAS,CAAA;wBAC3B,MAAM,MAAM,EAAE,eAAc,GAAI;4BAC/B,KAAK,EAAE,IAAI,CAAC,KAAK;4BACjB,KAAK,EAAE,UAAS;yBACjB,CAAA;wBACA,IAAI,CAAC,MAAM,CAAC,MAAM,CAAA,CAAA;oBACnB,CAAA;iBACA,CAAA,CAAA;aACF;QACD,CAAC;QAED,QAAQ,IAAI,IAAG;YACd,IAAI,IAAI,CAAC,MAAK,IAAK,IAAI,CAAC,UAAS,IAAK,IAAI,EAAE;gBAC3C,aAAY;gBACZ,MAAM,UAAS,GAAI,IAAI,CAAC,UAAU,EAAC,QAAQ,EAAC,CAAA;gBAC5C,GAAG,CAAC,UAAU,CAAC;oBACd,GAAG,EAAE,IAAI,CAAC,QAAQ;oBAClB,IAAI,EAAE,UAAS;iBACf,CAAA,CAAA;aACF;QACD,CAAC;QAED,QAAQ,IAAI,OAAM;YACjB,QAAO;YACP,IAAI,IAAI,CAAC,UAAS,IAAK,IAAI,EAAE;gBAC5B,IAAI,CAAC,SAAQ,GAAI,IAAG,CAAA;gBACpB,IAAI,CAAC,YAAW,GAAI,SAAQ,CAAA;gBAC5B,OAAO,KAAI,CAAA;aACZ;YACA,IAAI,CAAC,SAAQ,GAAI,KAAI,CAAA;YACrB,IAAI,CAAC,YAAW,GAAI,EAAC,CAAA;YACrB,OAAO,IAAG,CAAA;QACX,CAAC;QAED,MAAM,CAAC,KAAK,EAAE,eAAe,GAAG,IAAG;YAClC,QAAO;YACP,IAAI,CAAC,UAAS,GAAI,KAAK,CAAC,KAAI,CAAA;YAC5B,OAAM;YACN,IAAI,CAAC,QAAQ,EAAC,CAAA;YACd,UAAS;YACT,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,KAAK,CAAA,CAAA;QAC3B,CAAA;KACD;CACD,CAAA,CAAA;;;;;;;;WAzPA,GAAA,CAciB,yBAAA,EAAA,GAAA,CAAA;QAdA,KAAK,EAAE,IAAA,CAAA,SAAS;QAAG,YAAU,EAAE,IAAA,CAAA,SAAS;QAAG,GAAG,EAAE,IAAA,CAAA,GAAG;QAAG,eAAa,EAAE,IAAA,CAAA,YAAY;QAAG,aAAW,EAAE,IAAA,CAAA,UAAU;QAC1H,kBAAgB,EAAE,IAAA,CAAA,eAAe;;QACvB,eAAa,EAAA,WAAA,CACvB,IASO,GAAA,EAAA,CAAA,EAAA,CAAA;YATP,GAAA,CASO,MAAA,EAAA,GAAA,CAAA,EATD,KAAK,EAAC,iBAAiB,EAAA,CAAA,EAAA;gBAC5B,GAAA,CAOc,sBAAA,EAAA,GAAA,CAAA,EAPA,QAAM,EAAE,IAAA,CAAA,aAAa,EAAA,CAAA,EAAA,GAAA,CAAA;yCAEjC,IAAuC,GAAA,EAAA,CAAA,EAAA,CAAA;wBADxC,GAAA,CAKO,QAAA,EAAA,IAAA,EAAA,aAAA,CAAA,UAAA,CAJoB,IAAA,CAAA,YAAY,EAAA,CAA9B,MAAM,EAAE,KAAK,EAAb,OAAM,EAAA,OAAA,GAAA,GAAA,CAAA,EAAA;mCADf,GAAA,CAKO,MAAA,EAAA,GAAA,CAAA;gCALA,KAAK,EAAA,GAAA,CAAE,CAAA,cAAA,EAAA,GAAA,CAAA,EAAA,mBAAA,EAAA,KAAA,KAAA,IAAA,CAAA,YAAA,CAAA,MAAA,GAAA,CAAA,EAAA,CAAA,CAA4E,CAAA;gCAChD,GAAG,EAAE,KAAK;;gCACnD,GAAA,CACgD,gBAAA,EAAA,GAAA,CAAA;oCADxC,KAAK,EAAE,MAAM,CAAC,KAAK,CAAC,QAAQ,EAAA;oCAAK,OAAO,EAAE,IAAA,CAAA,SAAS,CAAC,MAAM,CAAC,KAAK,CAAA;oCACtE,KAAK,EAAE,IAAA,CAAA,UAAU;oCAAE,KAAK,EAAC,YAAY;;gCACvC,GAAA,CAAqF,MAAA,EAAA,GAAA,CAAA;oCAA/E,KAAK,EAAC,YAAY;oCAAE,OAAK,EAAA,GAAA,EAAA,GAAE,IAAA,CAAA,YAAY,CAAC,MAAM,CAAC,KAAK,CAAA,CAAA,CAAA,CAAA;wCAAM,MAAM,CAAC,IAAI,CAAA,EAAA,CAAA,CAAA,iBAAA,EAAA,CAAA,SAAA,CAAA,CAAA", "file": "components/main-form/components/form-radio.uvue", "sourcesContent": ["<template>\n\t<form-container :label=\"fieldName\" :show-error=\"showError\" :tip=\"tip\" :error-message=\"errorMessage\" :label-color=\"labelColor\"\n\t\t:background-color=\"backgroundColor\">\n\t\t<template #input-content>\n\t\t\t<view class=\"radio-container\">\n\t\t\t\t<radio-group @change=\"onRadioChange\">\n\t\t\t\t\t<view :class=\"['radio-option', { 'radio-option-last': index === radioOptions.length - 1 }]\"\n\t\t\t\t\t\tv-for=\"(option, index) in radioOptions\" :key=\"index\">\n\t\t\t\t\t\t<radio :value=\"option.value.toString()\" :checked=\"isChecked(option.value)\"\n\t\t\t\t\t\t\t:color=\"radioColor\" class=\"radio-item\"></radio>\n\t\t\t\t\t\t<text class=\"radio-text\" @click=\"selectOption(option.value)\">{{ option.text }}</text>\n\t\t\t\t\t</view>\n\t\t\t\t</radio-group>\n\t\t\t</view>\n\t\t</template>\n\t</form-container>\n</template>\n\n<script lang=\"uts\">\n\timport { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts'\n\timport FormContainer from './form-container.uvue'\n\n\t// 定义选项类型\n\ttype RadioOption = {\n\t\ttext: string;\n\t\tvalue: string | number;\n\t}\n\n\texport default {\n\t\tname: \"FormRadio\",\n\t\temits: ['change', 'msg'],\n\t\tcomponents: {\n\t\t\tFormContainer\n\t\t},\n\t\tprops: {\n\t\t\tdata: {\n\t\t\t\ttype: null as any as PropType<FormFieldData>\n\t\t\t},\n\t\t\tindex: {\n\t\t\t\ttype: Number,\n\t\t\t\tdefault: 0\n\t\t\t},\n\t\t\tkeyName: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"\"\n\t\t\t},\n\t\t\tlabelColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#000\"\n\t\t\t},\n\t\t\tbackgroundColor: {\n\t\t\t\ttype: String,\n\t\t\t\tdefault: \"#f1f4f9\"\n\t\t\t}\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tfieldName: \"\",\n\t\t\t\tfieldValue: null as string | number | null,\n\t\t\t\tisSave: false,\n\t\t\t\tsave_key: \"\",\n\t\t\t\ttip: \"\",\n\t\t\t\tvarType: \"string\",\n\t\t\t\tradioOptions: [] as RadioOption[],\n\t\t\t\tradioColor: \"#007aff\",\n\t\t\t\tshowError: false,\n\t\t\t\terrorMessage: \"\"\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\n\t\t},\n\t\twatch: {\n\t\t\tdata: {\n\t\t\t\thandler(obj: FormFieldData) {\n\t\t\t\t\t// 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue\n\t\t\t\t\tconst newValue = obj.value\n\t\t\t\t\tif (newValue !== this.fieldValue) {\n\t\t\t\t\t\tthis.fieldValue = newValue\n\t\t\t\t\t}\n\t\t\t\t},\n\t\t\t\tdeep: true\n\t\t\t}\n\t\t},\n\t\tcreated(): void {\n\t\t\t// 初始化时调用一次即可\n\t\t\tconst fieldObj = this.$props[\"data\"] as FormFieldData\n\t\t\tthis.initFieldData(fieldObj)\n\t\t},\n\t\tmethods: {\n\t\t\t// 初始化字段数据（仅在首次加载时调用）\n\t\t\tinitFieldData(fieldObj: FormFieldData): void {\n\t\t\t\tconst fieldKey = fieldObj.key\n\t\t\t\tconst fieldValue = fieldObj.value\n\n\t\t\t\t// 设置基本信息\n\t\t\t\tthis.fieldName = fieldObj.name\n\t\t\t\tthis.fieldValue = fieldValue\n\t\t\t\tthis.isSave = fieldObj.isSave ?? false\n\t\t\t\tthis.save_key = this.keyName + \"_\" + fieldKey\n\n\t\t\t\t// 解析配置信息\n\t\t\t\tconst extalJson = fieldObj.extra as UTSJSONObject\n\t\t\t\tthis.tip = extalJson.getString(\"tip\") ?? \"\"\n\t\t\t\tconst configVarType = extalJson.getString(\"varType\") ?? \"string\"\n\n\t\t\t\t// 校验 varType 的有效性\n\t\t\t\tthis.varType = this.validateVarType(configVarType)\n\t\t\t\tthis.radioColor = extalJson.getString(\"radioColor\") ?? \"#007aff\"\n\n\t\t\t\t// 解析选项数据\n\t\t\t\tconst optionsArray = extalJson.getArray(\"options\")\n\t\t\t\tif (optionsArray != null) {\n\t\t\t\t\tthis.radioOptions = []\n\t\t\t\t\tfor (let i = 0; i < optionsArray.length; i++) {\n\t\t\t\t\t\tconst optionObj = optionsArray[i] as UTSJSONObject\n\t\t\t\t\t\tconst option: RadioOption = {\n\t\t\t\t\t\t\ttext: optionObj.getString(\"text\") ?? \"\",\n\t\t\t\t\t\t\tvalue: optionObj.get(\"value\") ?? \"\"\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.radioOptions.push(option)\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// 获取缓存\n\t\t\t\tthis.getCache()\n\t\t\t},\n\n\t\t\t// 校验 varType 的有效性\n\t\t\tvalidateVarType(varType: string): string {\n\t\t\t\tconst validTypes = [\"string\", \"number\"]\n\t\t\t\tif (validTypes.includes(varType)) {\n\t\t\t\t\treturn varType\n\t\t\t\t} else {\n\t\t\t\t\treturn \"string\"\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 判断选项是否被选中\n\t\t\tisChecked(optionValue: string | number): boolean {\n\t\t\t\tif (this.fieldValue == null) {\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\t// 使用宽松比较来处理类型差异\n\t\t\t\treturn optionValue == this.fieldValue\n\t\t\t},\n\n\t\t\t// 选择选项\n\t\t\tselectOption(optionValue: string | number): void {\n\t\t\t\tlet selectedValue: string | number = optionValue\n\n\t\t\t\t// 根据varType转换类型\n\t\t\t\tif (this.varType == \"number\") {\n\t\t\t\t\tif (typeof selectedValue === \"number\") {\n\t\t\t\t\t\tselectedValue = selectedValue\n\t\t\t\t\t} else {\n\t\t\t\t\t\tselectedValue = parseFloat(selectedValue.toString())\n\t\t\t\t\t\tif (isNaN(selectedValue as number)) {\n\t\t\t\t\t\t\tselectedValue = 0\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t} else {\n\t\t\t\t\t// varType == \"string\"\n\t\t\t\t\tselectedValue = selectedValue.toString()\n\t\t\t\t}\n\n\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\tindex: this.index,\n\t\t\t\t\tvalue: selectedValue\n\t\t\t\t}\n\t\t\t\tthis.change(result)\n\t\t\t},\n\n\t\t\t// radio-group change事件处理\n\t\t\tonRadioChange(event: any): void {\n\t\t\t\tconst selectedValue = event.detail.value\n\t\t\t\t// 查找对应的选项\n\t\t\t\tconst selectedOption = this.radioOptions.find((option: RadioOption): boolean => {\n\t\t\t\t\treturn option.value.toString() == selectedValue\n\t\t\t\t})\n\n\t\t\t\tif (selectedOption != null) {\n\t\t\t\t\tthis.selectOption(selectedOption.value)\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tgetCache(): void {\n\t\t\t\tif (this.isSave) {\n\t\t\t\t\tconst that = this\n\t\t\t\t\tuni.getStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tsuccess: (res: GetStorageSuccess) => {\n\t\t\t\t\t\t\tconst cacheData = res.data as string\n\t\t\t\t\t\t\tlet save_value: string | number\n\n\t\t\t\t\t\t\t// 根据varType转换类型\n\t\t\t\t\t\t\tif (that.varType == \"number\") {\n\t\t\t\t\t\t\t\tsave_value = parseFloat(cacheData)\n\t\t\t\t\t\t\t\t// 验证转换结果\n\t\t\t\t\t\t\t\tif (isNaN(save_value as number)) {\n\t\t\t\t\t\t\t\t\treturn // 转换失败，不使用缓存\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\t// varType == \"string\"\n\t\t\t\t\t\t\t\tsave_value = cacheData\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tthat.fieldValue = save_value\n\t\t\t\t\t\t\tconst result: FormChangeEvent = {\n\t\t\t\t\t\t\t\tindex: this.index,\n\t\t\t\t\t\t\t\tvalue: save_value\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tthis.change(result)\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tsetCache(): void {\n\t\t\t\tif (this.isSave && this.fieldValue != null) {\n\t\t\t\t\t// 统一以字符串形式存储\n\t\t\t\t\tconst cacheValue = this.fieldValue.toString()\n\t\t\t\t\tuni.setStorage({\n\t\t\t\t\t\tkey: this.save_key,\n\t\t\t\t\t\tdata: cacheValue\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tvalidate(): boolean {\n\t\t\t\t// 单选器验证\n\t\t\t\tif (this.fieldValue == null) {\n\t\t\t\t\tthis.showError = true\n\t\t\t\t\tthis.errorMessage = \"请选择一个选项\"\n\t\t\t\t\treturn false\n\t\t\t\t}\n\t\t\t\tthis.showError = false\n\t\t\t\tthis.errorMessage = \"\"\n\t\t\t\treturn true\n\t\t\t},\n\n\t\t\tchange(event: FormChangeEvent): void {\n\t\t\t\t// 更新字段值\n\t\t\t\tthis.fieldValue = event.value\n\t\t\t\t// 保存缓存\n\t\t\t\tthis.setCache()\n\t\t\t\t// 触发父组件事件\n\t\t\t\tthis.$emit('change', event)\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style>\n\t.radio-container {\n\t\tflex: 1;\n\t\tpadding: 10rpx 20rpx;\n\t}\n\n\t.radio-option {\n\t\tdisplay: flex;\n\t\tflex-direction: row;\n\t\talign-items: center;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.radio-option-last {\n\t\tmargin-bottom: 0;\n\t}\n\n\t.radio-item {\n\t\tmargin-right: 20rpx;\n\t}\n\n\t.radio-text {\n\t\tflex: 1;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333333;\n\t\tline-height: 1.4;\n\t}\n</style>"]}