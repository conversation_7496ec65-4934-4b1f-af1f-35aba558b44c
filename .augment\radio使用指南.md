### `radio-group` 和 `radio` 的使用方法和注意事项

#### **1. `radio-group` 使用方法**
- **功能**：`radio-group` 是一个容器组件，用于包裹一组 `radio` 组件。它用于管理单选逻辑，确保在同一组 `radio-group` 中只能有一个 `radio` 被选中。
- **属性**：
  - **`@change`**：监听事件，当组内的 `radio` 选中状态发生变化时触发。事件回调会携带当前选中的 `radio` 的 `value` 值。
- **使用场景**：
  - 用于需要用户从多个选项中选择一个的场景，例如问卷调查、表单选择等。
- **示例代码**：
  ```html
  <radio-group @change="radioChange">
    <radio value="option1">选项1</radio>
    <radio value="option2">选项2</radio>
    <radio value="option3">选项3</radio>
  </radio-group>
  ```

#### **2. `radio` 使用方法**
- **功能**：`radio` 是单选按钮组件，通常用于 `radio-group` 内部，表示一个可选的选项。
- **属性**：
  - **`value`**：`radio` 的标识值。当该 `radio` 被选中时，`radio-group` 的 `change` 事件会携带此值。
  - **`checked`**：布尔值，表示该 `radio` 是否被选中。
  - **`disabled`**：布尔值，表示该 `radio` 是否被禁用。
  - **`color`**：`radio` 的颜色。
  - **`backgroundColor`**：`radio` 的默认背景颜色。
  - **`borderColor`**：`radio` 的默认边框颜色。
  - **`activeBackgroundColor`**：`radio` 被选中时的背景颜色。
  - **`activeBorderColor`**：`radio` 被选中时的边框颜色。
  - **`foreColor`**：`radio` 的图标颜色。
  - **`iconColor`**：`radio` 的图标颜色（已废弃，使用 `foreColor`）。
- **事件**：
  - **`@click`**：点击事件。
  - **`@touchstart`**：触摸开始事件。
  - **`@touchmove`**：触摸移动事件。
  - **`@touchcancel`**：触摸取消事件。
  - **`@touchend`**：触摸结束事件。
  - **`@tap`**：点击事件（等同于 `@click`）。
  - **`@longpress`**：长按事件。
- **使用场景**：
  - 在表单中提供多个选项供用户选择，每次只能选择一个。
- **示例代码**：
  ```html
  <radio-group @change="radioChange">
    <radio value="option1" :checked="current === 0">选项1</radio>
    <radio value="option2" :checked="current === 1">选项2</radio>
    <radio value="option3" :checked="current === 2">选项3</radio>
  </radio-group>
  ```

#### **3. 注意事项**
1. **`radio-group` 和 `radio` 的嵌套关系**：
   - `radio` 必须嵌套在 `radio-group` 内部，否则无法实现单选逻辑。
   - 同一个 `radio-group` 内的 `radio` 组件共享选中状态。

2. **`radio` 的 `checked` 属性**：
   - `checked` 属性用于控制 `radio` 的选中状态。在 `radio-group` 内部，只有一个 `radio` 可以被选中。
   - 如果需要动态控制 `radio` 的选中状态，可以通过绑定 `radio-group` 的 `change` 事件来更新 `radio` 的 `checked` 属性。

3. **`radio` 的 `value` 属性**：
   - 每个 `radio` 的 `value` 值必须是唯一的，用于标识不同的选项。
   - `radio-group` 的 `change` 事件会携带当前选中的 `radio` 的 `value` 值。

4. **禁用状态**：
   - 如果需要禁用某个 `radio`，可以设置其 `disabled` 属性为 `true`。
   - 禁用的 `radio` 无法被选中，也无法触发任何事件。

5. **样式自定义**：
   - `radio` 提供了多种样式相关的属性，如 `color`、`backgroundColor`、`borderColor` 等，可以通过这些属性自定义 `radio` 的外观。
   - 注意 `activeBackgroundColor` 和 `activeBorderColor` 的优先级高于 `color` 属性。

6. **事件监听**：
   - `radio` 提供了丰富的事件监听，如 `click`、`touchstart`、`touchmove` 等，可以根据需要绑定这些事件来实现更复杂的交互逻辑。

7. **兼容性**：
   - `radio` 和 `radio-group` 在 Web、微信小程序、Android、iOS、HarmonyOS 等平台上的兼容性良好，但需要注意不同平台的样式表现可能略有差异。

通过以上方法和注意事项，可以更好地使用 `radio-group` 和 `radio` 组件来实现单选功能。