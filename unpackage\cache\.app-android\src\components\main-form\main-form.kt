@file:Suppress("UNCHECKED_CAST", "USELESS_CAST", "INAPPLICABLE_JVM_NAME", "UNUSED_ANONYMOUS_PARAMETER", "NAME_SHADOWING", "UNNECESSARY_NOT_NULL_ASSERTION")
package uni.UNIC178CB1
import io.dcloud.uniapp.*
import io.dcloud.uniapp.extapi.*
import io.dcloud.uniapp.framework.*
import io.dcloud.uniapp.runtime.*
import io.dcloud.uniapp.vue.*
import io.dcloud.uniapp.vue.shared.*
import io.dcloud.unicloud.*
import io.dcloud.uts.*
import io.dcloud.uts.Map
import io.dcloud.uts.Set
import io.dcloud.uts.UTSAndroid
open class GenComponentsMainFormMainForm : VueComponent {
    constructor(__ins: ComponentInternalInstance) : super(__ins) {}
    @Suppress("UNUSED_PARAMETER", "UNUSED_VARIABLE")
    override fun `$render`(): Any? {
        val _ctx = this
        val _cache = this.`$`.renderCache
        val _component_FormInput = resolveComponent("FormInput")
        val _component_FormTextarea = resolveComponent("FormTextarea")
        val _component_FormSwitch = resolveComponent("FormSwitch")
        val _component_FormSlider = resolveComponent("FormSlider")
        val _component_FormNumberbox = resolveComponent("FormNumberbox")
        val _component_FormColor = resolveComponent("FormColor")
        val _component_FormSelect = resolveComponent("FormSelect")
        val _component_FormRadio = resolveComponent("FormRadio")
        val _component_FormYearmonth = resolveComponent("FormYearmonth")
        val _component_FormDatetime = resolveComponent("FormDatetime")
        return _cE("view", _uM("class" to "main-form"), _uA(
            if (_ctx.title != "") {
                _cE("view", _uM("key" to 0, "class" to "main-form-title"), _uA(
                    _cE("text", null, _tD(_ctx.title), 1)
                ))
            } else {
                _cC("v-if", true)
            }
            ,
            _cE("view", _uM("class" to "main-form-content"), _uA(
                _cE(Fragment, null, RenderHelpers.renderList(_ctx.formData, fun(item, index, __index, _cached): Any {
                    return _cE("view", _uM("class" to "main-form-item"), _uA(
                        if (item.type == "input") {
                            _cV(_component_FormInput, _uM("key" to 0, "data" to item, "index" to index, "keyName" to _ctx.keyName, "onChange" to _ctx.change, "onMsg" to _ctx.receiveMsg), null, 8, _uA(
                                "data",
                                "index",
                                "keyName",
                                "onChange",
                                "onMsg"
                            ))
                        } else {
                            _cC("v-if", true)
                        }
                        ,
                        if (item.type == "textarea") {
                            _cV(_component_FormTextarea, _uM("key" to 1, "data" to item, "index" to index, "keyName" to _ctx.keyName, "onChange" to _ctx.change, "onMsg" to _ctx.receiveMsg), null, 8, _uA(
                                "data",
                                "index",
                                "keyName",
                                "onChange",
                                "onMsg"
                            ))
                        } else {
                            _cC("v-if", true)
                        }
                        ,
                        if (item.type == "switch") {
                            _cV(_component_FormSwitch, _uM("key" to 2, "data" to item, "index" to index, "keyName" to _ctx.keyName, "onChange" to _ctx.change, "onMsg" to _ctx.receiveMsg), null, 8, _uA(
                                "data",
                                "index",
                                "keyName",
                                "onChange",
                                "onMsg"
                            ))
                        } else {
                            _cC("v-if", true)
                        }
                        ,
                        if (item.type == "slider") {
                            _cV(_component_FormSlider, _uM("key" to 3, "data" to item, "index" to index, "keyName" to _ctx.keyName, "onChange" to _ctx.change, "onMsg" to _ctx.receiveMsg), null, 8, _uA(
                                "data",
                                "index",
                                "keyName",
                                "onChange",
                                "onMsg"
                            ))
                        } else {
                            _cC("v-if", true)
                        }
                        ,
                        if (item.type == "numberbox") {
                            _cV(_component_FormNumberbox, _uM("key" to 4, "data" to item, "index" to index, "keyName" to _ctx.keyName, "onChange" to _ctx.change, "onMsg" to _ctx.receiveMsg), null, 8, _uA(
                                "data",
                                "index",
                                "keyName",
                                "onChange",
                                "onMsg"
                            ))
                        } else {
                            _cC("v-if", true)
                        }
                        ,
                        if (item.type == "color") {
                            _cV(_component_FormColor, _uM("key" to 5, "data" to item, "index" to index, "keyName" to _ctx.keyName, "onChange" to _ctx.change, "onMsg" to _ctx.receiveMsg), null, 8, _uA(
                                "data",
                                "index",
                                "keyName",
                                "onChange",
                                "onMsg"
                            ))
                        } else {
                            _cC("v-if", true)
                        }
                        ,
                        if (item.type == "select") {
                            _cV(_component_FormSelect, _uM("key" to 6, "data" to item, "index" to index, "keyName" to _ctx.keyName, "onChange" to _ctx.change, "onMsg" to _ctx.receiveMsg), null, 8, _uA(
                                "data",
                                "index",
                                "keyName",
                                "onChange",
                                "onMsg"
                            ))
                        } else {
                            _cC("v-if", true)
                        }
                        ,
                        if (item.type == "radio") {
                            _cV(_component_FormRadio, _uM("key" to 7, "data" to item, "index" to index, "keyName" to _ctx.keyName, "onChange" to _ctx.change, "onMsg" to _ctx.receiveMsg), null, 8, _uA(
                                "data",
                                "index",
                                "keyName",
                                "onChange",
                                "onMsg"
                            ))
                        } else {
                            _cC("v-if", true)
                        }
                        ,
                        if (item.type == "yearmonth") {
                            _cV(_component_FormYearmonth, _uM("key" to 8, "data" to item, "index" to index, "keyName" to _ctx.keyName, "onChange" to _ctx.change, "onMsg" to _ctx.receiveMsg), null, 8, _uA(
                                "data",
                                "index",
                                "keyName",
                                "onChange",
                                "onMsg"
                            ))
                        } else {
                            _cC("v-if", true)
                        }
                        ,
                        if (item.type == "datetime") {
                            _cV(_component_FormDatetime, _uM("key" to 9, "data" to item, "index" to index, "keyName" to _ctx.keyName, "onChange" to _ctx.change, "onMsg" to _ctx.receiveMsg), null, 8, _uA(
                                "data",
                                "index",
                                "keyName",
                                "onChange",
                                "onMsg"
                            ))
                        } else {
                            _cC("v-if", true)
                        }
                    ))
                }
                ), 256)
            ))
        ))
    }
    open var formData: UTSArray<FormFieldData> by `$props`
    open var title: String by `$props`
    open var keyName: String by `$props`
    open var receiveMsg = ::gen_receiveMsg_fn
    open fun gen_receiveMsg_fn(msg: MsgEvent) {}
    open var change = ::gen_change_fn
    open fun gen_change_fn(event: FormChangeEvent) {
        val fieldIndex = event.index as Number
        val fieldValue = event.value as Any
        this.formData[fieldIndex].value = fieldValue
    }
    companion object {
        var name = "main-form"
        val styles: Map<String, Map<String, Map<String, Any>>> by lazy {
            _nCS(_uA(
                styles0
            ))
        }
        val styles0: Map<String, Map<String, Map<String, Any>>>
            get() {
                return _uM("main-form" to _pS(_uM("width" to "100%", "display" to "flex", "flexDirection" to "column")), "main-form-title" to _pS(_uM("width" to "710rpx", "marginTop" to 0, "marginRight" to "auto", "marginBottom" to "20rpx", "marginLeft" to "auto", "paddingTop" to 0, "paddingRight" to "20rpx", "paddingBottom" to 0, "paddingLeft" to "20rpx", "borderLeftWidth" to "10rpx", "borderLeftStyle" to "solid", "borderLeftColor" to "#1F6ED4", "display" to "flex", "alignItems" to "center")), "main-form-content" to _pS(_uM("width" to "100%", "display" to "flex", "flexDirection" to "column")), "main-form-item" to _pS(_uM("width" to "100%")))
            }
        var inheritAttrs = true
        var inject: Map<String, Map<String, Any?>> = _uM()
        var emits: Map<String, Any?> = _uM()
        var props = _nP(_uM("formData" to _uM("type" to "Array", "default" to fun(): UTSArray<FormFieldData> {
            return _uA()
        }
        ), "title" to _uM("type" to "String", "default" to ""), "keyName" to _uM("type" to "String", "default" to "")))
        var propsNeedCastKeys = _uA(
            "formData",
            "title",
            "keyName"
        )
        var components: Map<String, CreateVueComponent> = _uM("FormInput" to GenComponentsMainFormComponentsFormInputClass, "FormTextarea" to GenComponentsMainFormComponentsFormTextareaClass, "FormSwitch" to GenComponentsMainFormComponentsFormSwitchClass, "FormSlider" to GenComponentsMainFormComponentsFormSliderClass, "FormNumberbox" to GenComponentsMainFormComponentsFormNumberboxClass, "FormColor" to GenComponentsMainFormComponentsFormColorClass, "FormSelect" to GenComponentsMainFormComponentsFormSelectClass, "FormRadio" to GenComponentsMainFormComponentsFormRadioClass, "FormYearmonth" to GenComponentsMainFormComponentsFormYearmonthClass, "FormDatetime" to GenComponentsMainFormComponentsFormDatetimeClass)
    }
}
