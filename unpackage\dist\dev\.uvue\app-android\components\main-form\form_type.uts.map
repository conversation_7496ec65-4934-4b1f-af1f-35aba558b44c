{"version": 3, "file": "form_type.uts", "sourceRoot": "", "sources": ["components/main-form/form_type.uts"], "names": [], "mappings": "AAAA,MAAM,MAAM,aAAa,GAAG;IAC3B,GAAG,EAAG,MAAM,CAAC;IACb,IAAI,EAAG,MAAM,CAAC;IACd,IAAI,EAAG,MAAM,CAAC;IACd,KAAK,EAAG,GAAG,CAAC;IACZ,MAAO,CAAC,EAAE,OAAO,CAAC;IAClB,SAAU,CAAC,EAAE,MAAM,CAAC;IACpB,KAAK,EAAG,aAAa,CAAA;CACrB,CAAA;AAED,MAAM,MAAM,eAAe,GAAG;IAC7B,KAAK,EAAG,MAAM,CAAC;IACf,KAAK,EAAG,GAAG,CAAA;CACX,CAAA;AAED,MAAM,MAAM,QAAQ,GAAG;IACtB,IAAI,EAAG,MAAM,CAAC;IACd,GAAG,EAAG,GAAG,CAAC;IACV,IAAI,CAAC,EAAC,GAAG,CAAA;CACT,CAAA;AAGD,MAAM,MAAM,eAAe,GAAG;IAC5B,KAAK,EAAE,MAAM,CAAC;IACd,KAAK,EAAE,IAAI,GAAG,IAAI,EAAE,CAAC;IACrB,WAAW,CAAC,EAAE,OAAO,CAAA;CACrB,CAAA", "sourcesContent": ["export type FormFieldData = {\r\n\tkey : string;\r\n\tname : string;\r\n\ttype : string;\r\n\tvalue : any;\r\n\tisSave ?: boolean;\r\n\tcondition ?: string;\r\n\textra : UTSJSONObject\r\n}\r\n\r\nexport type FormChangeEvent = {\r\n\tindex : number;\r\n\tvalue : any\r\n}\r\n\r\nexport type MsgEvent = {\r\n\ttype : string;\r\n\tmsg : any;\r\n\tdata?:any\r\n}\r\n\r\n\r\nexport type DateQuickOption = {\r\n\t\tlabel: string,\r\n\t\tvalue: Date | Date[],\r\n\t\tautoConfirm?: boolean\r\n\t}"]}