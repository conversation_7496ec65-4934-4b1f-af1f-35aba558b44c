export type FormFieldData = {
    key: string;
    name: string;
    type: string;
    value: any;
    isSave?: boolean;
    condition?: string;
    extra: UTSJSONObject;
};
export type FormChangeEvent = {
    index: number;
    value: any;
};
export type MsgEvent = {
    type: string;
    msg: any;
    data?: any;
};
export type DateQuickOption = {
    label: string;
    value: Date | Date[];
    autoConfirm?: boolean;
};
//# sourceMappingURL=form_type.uts.map