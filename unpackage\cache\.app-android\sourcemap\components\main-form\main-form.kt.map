{"version": 3, "sources": ["components/main-form/main-form.uvue"], "sourcesContent": ["<template>\r\n\t<view class=\"main-form\" >\r\n\t\t<!-- 表单标题 -->\r\n\t\t<view class=\"main-form-title\" v-if=\"title != ''\" >\r\n\t\t\t<text >{{ title }}</text>\r\n\t\t</view>\r\n\r\n\t\t<!-- 表单项容器 -->\r\n\t\t<view class=\"main-form-content\">\r\n\t\t\t<view class=\"main-form-item\" v-for=\"(item, index) in formData\">\r\n\t\t\t\t<FormInput v-if=\"item.type=='input'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormInput>\r\n\t\t\t\t<FormTextarea v-if=\"item.type=='textarea'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormTextarea>\r\n\t\t\t\t<FormSwitch v-if=\"item.type=='switch'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormSwitch>\r\n\t\t\t\t<FormSlider v-if=\"item.type=='slider'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormSlider>\r\n\t\t\t\t<FormNumberbox v-if=\"item.type=='numberbox'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormNumberbox>\r\n\t\t\t\t<FormColor v-if=\"item.type=='color'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormColor>\r\n\t\t\t\t<FormSelect v-if=\"item.type=='select'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormSelect>\r\n\t\t\t\t<FormYearmonth v-if=\"item.type=='yearmonth'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormYearmonth>\r\n\t\t\t\t<FormDatetime v-if=\"item.type=='datetime'\" :data=\"item\" :index=\"index\" :keyName=\"keyName\" @change=\"change\" @msg=\"receiveMsg\"></FormDatetime>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script lang=\"uts\">\r\n\t// 导入表单组件\r\n\timport FormInput from './components/form-input.uvue'\r\n\timport FormTextarea from './components/form-textarea.uvue'\r\n\timport FormSwitch from './components/form-switch.uvue'\r\n\timport FormSlider from './components/form-slider.uvue'\r\n\timport FormNumberbox from './components/form-numberbox.uvue'\r\n\timport FormColor from './components/form-color.uvue'\r\n\timport FormSelect from './components/form-select.uvue'\r\n\timport FormYearmonth from './components/form-yearmonth.uvue'\r\n\timport FormDatetime from './components/form-datetime.uvue'\r\n\timport { FormFieldData ,FormChangeEvent,MsgEvent} from '@/components/main-form/form_type.uts'\r\n\r\n\r\n\r\n\r\n\texport default {\r\n\t\tname: \"main-form\",\r\n\t\tcomponents: {\r\n\t\t\tFormInput,\r\n\t\t\tFormTextarea,\r\n\t\t\tFormSwitch,\r\n\t\t\tFormSlider,\r\n\t\t\tFormNumberbox,\r\n\t\t\tFormColor,\r\n\t\t\tFormSelect,\r\n\t\t\tFormYearmonth,\r\n\t\t\tFormDatetime\r\n\t\t},\r\n\t\tprops: {\r\n\t\t\t// 表单数据数组\r\n\t\t\tformData: {\r\n\t\t\t\ttype: Array as PropType<FormFieldData[]>,\r\n\t\t\t\tdefault: () : Array<FormFieldData> => []\r\n\t\t\t},\r\n\t\t\t// 表单标题\r\n\t\t\ttitle: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t},\r\n\r\n\t\t\t// 存储键名前缀\r\n\t\t\tkeyName: {\r\n\t\t\t\ttype: String,\r\n\t\t\t\tdefault: \"\"\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\treceiveMsg(msg :MsgEvent){},\r\n\t\t\tchange(event:FormChangeEvent){\r\n\r\n\t\t\t\t// 获取当前字段索引\r\n\t\t\t\tconst fieldIndex = event.index as number\r\n\t\t\t\tconst fieldValue=event.value as any\r\n\t\t\t\tthis.formData[fieldIndex].value=fieldValue \r\n\t\t\t\t\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.main-form {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.main-form-title {\r\n\t\twidth: 710rpx;\r\n\t\tmargin: 0 auto;\r\n\t\tpadding: 0 20rpx;\r\n\t\tmargin-bottom: 20rpx;\r\n\t\tborder-left: 10rpx solid #1F6ED4;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.main-form-content {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\tflex-direction: column;\r\n\t}\r\n\r\n\t.main-form-item {\r\n\t\twidth: 100%;\r\n\t}\r\n</style>"], "names": [], "mappings": ";;;;;;;;;;;;;AAwCM;;;;;;;;;;;;;;;eAvCL,IAoBO,QAAA,IApBD,WAAM,cAAW;YAEc,IAAA,KAAA,KAAK,IAAA;gBAAzC,IAEO,QAAA,gBAFD,WAAM;oBACX,IAAyB,QAAA,IAAA,EAAA,IAAf,KAAA,KAAK,GAAA,CAAA;;;;;;YAIhB,IAYO,QAAA,IAZD,WAAM,sBAAmB;gBAC9B,IAUO,UAAA,IAAA,EAAA,cAAA,UAAA,CAV8C,KAAA,QAAQ,EAAA,IAAxB,MAAM,OAAN,SAAI,UAAA,GAAA,CAAA;2BAAzC,IAUO,QAAA,IAVD,WAAM,mBAAgB;wBACV,IAAA,KAAK,IAAI,IAAA;4BAA1B,IAAmI,sBAAA,gBAA7F,UAAM,MAAO,WAAO,OAAQ,aAAS,KAAA,OAAO,EAAG,cAAQ,KAAA,MAAM,EAAG,WAAK,KAAA,UAAU;;;;;;;;;;;wBACjG,IAAA,KAAK,IAAI,IAAA;4BAA7B,IAA4I,yBAAA,gBAAhG,UAAM,MAAO,WAAO,OAAQ,aAAS,KAAA,OAAO,EAAG,cAAQ,KAAA,MAAM,EAAG,WAAK,KAAA,UAAU;;;;;;;;;;;wBACzG,IAAA,KAAK,IAAI,IAAA;4BAA3B,IAAsI,uBAAA,gBAA9F,UAAM,MAAO,WAAO,OAAQ,aAAS,KAAA,OAAO,EAAG,cAAQ,KAAA,MAAM,EAAG,WAAK,KAAA,UAAU;;;;;;;;;;;wBACrG,IAAA,KAAK,IAAI,IAAA;4BAA3B,IAAsI,uBAAA,gBAA9F,UAAM,MAAO,WAAO,OAAQ,aAAS,KAAA,OAAO,EAAG,cAAQ,KAAA,MAAM,EAAG,WAAK,KAAA,UAAU;;;;;;;;;;;wBAClG,IAAA,KAAK,IAAI,IAAA;4BAA9B,IAA+I,0BAAA,gBAAjG,UAAM,MAAO,WAAO,OAAQ,aAAS,KAAA,OAAO,EAAG,cAAQ,KAAA,MAAM,EAAG,WAAK,KAAA,UAAU;;;;;;;;;;;wBAC5G,IAAA,KAAK,IAAI,IAAA;4BAA1B,IAAmI,sBAAA,gBAA7F,UAAM,MAAO,WAAO,OAAQ,aAAS,KAAA,OAAO,EAAG,cAAQ,KAAA,MAAM,EAAG,WAAK,KAAA,UAAU;;;;;;;;;;;wBACnG,IAAA,KAAK,IAAI,IAAA;4BAA3B,IAAsI,uBAAA,gBAA9F,UAAM,MAAO,WAAO,OAAQ,aAAS,KAAA,OAAO,EAAG,cAAQ,KAAA,MAAM,EAAG,WAAK,KAAA,UAAU;;;;;;;;;;;wBAClG,IAAA,KAAK,IAAI,IAAA;4BAA9B,IAA+I,0BAAA,gBAAjG,UAAM,MAAO,WAAO,OAAQ,aAAS,KAAA,OAAO,EAAG,cAAQ,KAAA,MAAM,EAAG,WAAK,KAAA,UAAU;;;;;;;;;;;wBACzG,IAAA,KAAK,IAAI,IAAA;4BAA7B,IAA4I,yBAAA,gBAAhG,UAAM,MAAO,WAAO,OAAQ,aAAS,KAAA,OAAO,EAAG,cAAQ,KAAA,MAAM,EAAG,WAAK,KAAA,UAAU;;;;;;;;;;;;;;;;;;;aAyD5H;aAAA,kBAAW,aAAa,EAAA,CAAE;aAC1B;aAAA,cAAO,sBAAqB,EAAA;QAG3B,IAAM,aAAa,MAAM,KAAI,CAAA,EAAA,CAAK,MAAK;QACvC,IAAM,aAAW,MAAM,KAAI,CAAA,EAAA,CAAK,GAAE;QAClC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,KAAK,GAAC;IAEjC;;mBA1CK;;;;;;;;;;;;;8EAgBK,OAAK;mBAAwB,KAAC;;2DAK9B,uDAMA;;;;;;;;AAiBZ"}