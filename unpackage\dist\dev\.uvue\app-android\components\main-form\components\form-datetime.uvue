import { FormFieldData, FormChangeEvent } from '@/components/main-form/form_type.uts';
import FormContainer from './form-container.uvue';
import MainDateTimePicker from './../tools/main-datetime-picker.uvue';
// 定义模式类型
type DateTimeMode = 'datetime' | 'date' | 'time' | 'year' | 'year-month' | 'month' | 'day' | 'hour-minute' | 'hour-minute-second' | 'datetime-range' | 'date-range' | 'time-range';
const __sfc__ = defineComponent({
    name: "FormDateTime",
    emits: ['change'],
    components: {
        FormContainer,
        MainDateTimePicker
    },
    props: {
        data: {
            type: null as any as PropType<FormFieldData>
        },
        index: {
            type: Number,
            default: 0
        },
        keyName: {
            type: String,
            default: ""
        },
        labelColor: {
            type: String,
            default: "#000"
        },
        backgroundColor: {
            type: String,
            default: "#f1f4f9"
        }
    },
    data() {
        return {
            fieldName: "",
            fieldValue: "" as string,
            isSave: false,
            save_key: "",
            tip: "",
            varType: "datetime" as DateTimeMode,
            displayValue: "",
            showError: false,
            errorMessage: ""
        };
    },
    computed: {},
    watch: {
        data: {
            handler(obj: FormFieldData) {
                // 只处理value的变化，当外部传入的value与当前fieldValue不同时，才更新fieldValue
                // 这避免了用户输入时的循环更新问题
                const newValue = obj.value as string;
                if (newValue !== this.fieldValue) {
                    this.fieldValue = newValue;
                    this.updateDisplayValue();
                }
            },
            deep: true
        }
    },
    created(): void {
        // 初始化时调用一次即可
        const fieldObj = this.$props["data"] as FormFieldData;
        this.initFieldData(fieldObj);
    },
    methods: {
        // 初始化字段数据（仅在首次加载时调用）
        initFieldData(fieldObj: FormFieldData): void {
            const fieldKey = fieldObj.key;
            const fieldValue = fieldObj.value as string;
            // 设置基本信息
            this.fieldName = fieldObj.name;
            this.fieldValue = fieldValue;
            this.isSave = fieldObj.isSave ?? false;
            this.save_key = this.keyName + "_" + fieldKey;
            // 解析配置信息
            const extalJson = fieldObj.extra as UTSJSONObject;
            this.tip = extalJson.getString("tip") ?? "";
            this.varType = extalJson.getString("varType") ?? "datetime" as DateTimeMode;
            // 更新显示值
            this.updateDisplayValue();
            // 获取缓存
            this.getCache();
        },
        // 更新显示值
        updateDisplayValue(): void {
            if (this.fieldValue != "") {
                this.displayValue = this.fieldValue as string;
            }
            else {
                this.displayValue = "";
            }
        },
        getCache(): void {
            if (this.isSave) {
                const that = this;
                uni.getStorage({
                    key: this.save_key,
                    success: (res: GetStorageSuccess) => {
                        const save_value = res.data;
                        if (typeof save_value === 'string') {
                            that.fieldValue = save_value as string;
                            that.updateDisplayValue();
                            const result: FormChangeEvent = {
                                index: this.index,
                                value: save_value
                            };
                            this.change(result);
                        }
                    }
                });
            }
        },
        setCache(): void {
            if (this.isSave && typeof this.fieldValue === "string") {
                uni.setStorage({
                    key: this.save_key,
                    data: this.fieldValue
                });
            }
        },
        validate(): boolean {
            // 时间值验证
            if (this.fieldValue == "") {
                this.showError = true;
                this.errorMessage = "请选择时间";
                return false;
            }
            this.showError = false;
            this.errorMessage = "";
            return true;
        },
        change(event: FormChangeEvent): void {
            // 更新字段值
            this.fieldValue = event.value as string;
            // 更新显示值
            this.updateDisplayValue();
            // 保存缓存
            this.setCache();
            // 触发父组件事件
            this.$emit('change', event);
        },
        // 打开时间选择器
        openDateTimePicker(): void {
            const datetimePicker = this.$refs["datetimePicker"] as ComponentPublicInstance;
            // 如果有值，传递给选择器
            if (this.fieldValue != "") {
                datetimePicker.$callMethod("show", this.fieldValue);
            }
            else {
                datetimePicker.$callMethod("show");
            }
        },
        // 时间选择确认
        onDateTimeConfirm(dateTimeData: UTSJSONObject): void {
            // 使用formatted的值作为最终结果
            const selectedDateTime = dateTimeData.getString("formatted") ?? "";
            const result: FormChangeEvent = {
                index: this.index,
                value: selectedDateTime
            };
            this.change(result);
        },
        // 时间选择取消
        onDateTimeCancel(): void {
            // 取消选择，不做任何操作
        }
    }
});
export default __sfc__;
function GenComponentsMainFormComponentsFormDatetimeRender(this: InstanceType<typeof __sfc__>): any | null {
    const _ctx = this;
    const _cache = this.$.renderCache;
    const _component_form_container = resolveComponent("form-container");
    const _component_MainDateTimePicker = resolveComponent("MainDateTimePicker");
    return _cE(Fragment, null, [
        _cV(_component_form_container, _uM({
            label: _ctx.fieldName,
            "show-error": _ctx.showError,
            tip: _ctx.tip,
            "error-message": _ctx.errorMessage,
            "label-color": _ctx.labelColor,
            "background-color": _ctx.backgroundColor
        }), _uM({
            "input-content": withSlotCtx((): any[] => [
                _cE("view", _uM({
                    class: "datetime-display-container",
                    onClick: _ctx.openDateTimePicker
                }), [
                    _ctx.displayValue !== ''
                        ? _cE("text", _uM({
                            key: 0,
                            class: "datetime-text"
                        }), _tD(_ctx.displayValue), 1 /* TEXT */)
                        : _cC("v-if", true),
                    _ctx.displayValue === ''
                        ? _cE("text", _uM({
                            key: 1,
                            class: "datetime-placeholder"
                        }), "请选择时间")
                        : _cC("v-if", true)
                ], 8 /* PROPS */, ["onClick"])
            ]),
            _: 1 /* STABLE */
        }), 8 /* PROPS */, ["label", "show-error", "tip", "error-message", "label-color", "background-color"]),
        _cV(_component_MainDateTimePicker, _uM({
            ref: "datetimePicker",
            mode: _ctx.varType,
            onConfirm: _ctx.onDateTimeConfirm,
            onCancel: _ctx.onDateTimeCancel
        }), null, 8 /* PROPS */, ["mode", "onConfirm", "onCancel"])
    ], 64 /* STABLE_FRAGMENT */);
}
const GenComponentsMainFormComponentsFormDatetimeStyles = [_uM([["datetime-display-container", _pS(_uM([["flex", 1], ["display", "flex"], ["flexDirection", "row"], ["alignItems", "center"], ["paddingTop", 0], ["paddingRight", "20rpx"], ["paddingBottom", 0], ["paddingLeft", "20rpx"], ["height", "60rpx"], ["borderTopLeftRadius", "10rpx"], ["borderTopRightRadius", "10rpx"], ["borderBottomRightRadius", "10rpx"], ["borderBottomLeftRadius", "10rpx"], ["backgroundColor", "rgba(255,255,255,0.8)"]]))], ["datetime-text", _pS(_uM([["flex", 1], ["fontSize", "28rpx"], ["color", "#333333"], ["textAlign", "left"]]))], ["datetime-placeholder", _pS(_uM([["flex", 1], ["fontSize", "28rpx"], ["color", "#999999"], ["textAlign", "center"]]))]])];
//# sourceMappingURL=form-datetime.uvue.map